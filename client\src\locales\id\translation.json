{"chat_direction_left_to_right": "<PERSON> yang perlu diisi di sini. <PERSON><PERSON><PERSON> kosong.", "chat_direction_right_to_left": "<PERSON> yang perlu diisi di sini. <PERSON><PERSON><PERSON> kosong", "com_a11y_ai_composing": "AI sedang menulis.", "com_a11y_end": "AI sudah selesai membalas.", "com_a11y_start": "AI telah mulai membalas.", "com_agents_allow_editing": "Izinkan pengguna lain untuk mengedit agen Anda", "com_agents_by_librechat": "<PERSON><PERSON><PERSON>", "com_auth_already_have_account": "Sudah memiliki akun?", "com_auth_click": "Klik", "com_auth_click_here": "<PERSON><PERSON> di sini", "com_auth_continue": "Lanjutkan", "com_auth_create_account": "<PERSON><PERSON><PERSON> akun <PERSON>", "com_auth_discord_login": "<PERSON><PERSON><PERSON>", "com_auth_email": "Email", "com_auth_email_address": "<PERSON><PERSON><PERSON> email", "com_auth_email_max_length": "<PERSON>ail tidak boleh lebih dari 120 karakter", "com_auth_email_min_length": "Email harus setidaknya 6 karakter", "com_auth_email_pattern": "Anda harus memasukkan alamat email yang valid", "com_auth_email_required": "<PERSON><PERSON>", "com_auth_error_create": "<PERSON> kesalahan saat mencoba mendaftarkan akun <PERSON>. <PERSON>lakan coba lagi.", "com_auth_error_invalid_reset_token": "Token pengaturan ulang kata sandi ini tidak lagi valid.", "com_auth_error_login": "Tidak dapat masuk dengan informasi yang diberikan. <PERSON>lakan periksa kredensial Anda dan coba lagi.", "com_auth_error_login_ban": "<PERSON><PERSON><PERSON>a telah dilarang sementara karena pelanggaran layanan kami.", "com_auth_error_login_rl": "<PERSON><PERSON><PERSON><PERSON> banyak upaya masuk dalam waktu singkat. <PERSON>lakan coba lagi nanti.", "com_auth_error_login_server": "Ada kesalahan server internal. Harap tunggu beberapa saat dan coba lagi.", "com_auth_facebook_login": "Ma<PERSON>k <PERSON>", "com_auth_full_name": "<PERSON><PERSON> le<PERSON>", "com_auth_github_login": "<PERSON><PERSON><PERSON>", "com_auth_google_login": "Ma<PERSON>k <PERSON>", "com_auth_here": "DI SINI", "com_auth_login": "<PERSON><PERSON><PERSON>", "com_auth_login_with_new_password": "<PERSON><PERSON> sekarang dapat masuk dengan kata sandi baru Anda.", "com_auth_name_max_length": "<PERSON>a harus kurang dari 80 karakter", "com_auth_name_min_length": "Nama harus setidaknya 3 karakter", "com_auth_name_required": "<PERSON><PERSON>", "com_auth_no_account": "Tidak memiliki akun?", "com_auth_password": "<PERSON>a sandi", "com_auth_password_confirm": "Konfirmasi kata sandi", "com_auth_password_forgot": "Lupa Kata Sandi?", "com_auth_password_max_length": "Kata sandi harus kurang dari 128 karakter", "com_auth_password_min_length": "Kata sandi harus setidaknya 8 karakter", "com_auth_password_not_match": "Kata sandi tidak cocok", "com_auth_password_required": "<PERSON>a sandi <PERSON>an", "com_auth_reset_password": "Atur ulang kata sandi Anda", "com_auth_reset_password_link_sent": "<PERSON><PERSON>", "com_auth_reset_password_success": "<PERSON><PERSON><PERSON><PERSON> Kat<PERSON> Sand<PERSON>", "com_auth_sign_in": "<PERSON><PERSON><PERSON>", "com_auth_sign_up": "<PERSON><PERSON><PERSON>", "com_auth_submit_registration": "<PERSON><PERSON>", "com_auth_to_reset_your_password": "untuk mengatur ulang kata sandi Anda.", "com_auth_to_try_again": "untuk mencoba lagi.", "com_auth_username": "<PERSON><PERSON> (opsional)", "com_auth_username_max_length": "<PERSON>a pengguna harus kurang dari 20 karakter", "com_auth_username_min_length": "Nama pengguna harus setidaknya 2 karakter", "com_auth_welcome_back": "Selamat datang kembali", "com_endpoint": "Endpoint", "com_endpoint_agent": "Agen", "com_endpoint_agent_model": "Model Agen (Direkomendasikan: GPT-3.5)", "com_endpoint_anthropic_maxoutputtokens": "<PERSON><PERSON><PERSON> maksimum token yang dapat dihasilkan dalam respons. Tentukan nilai yang lebih rendah untuk respons yang lebih pendek dan nilai yang lebih tinggi untuk respons yang lebih panjang.", "com_endpoint_anthropic_temp": "Berkisar dari 0 hingga 1. Gunakan temp yang lebih dekat ke 0 untuk analitis / pilihan ganda, dan lebih dekat ke 1 untuk tugas kreatif dan generatif. <PERSON><PERSON>n untuk mengubah ini atau Top P tetapi tidak keduanya.", "com_endpoint_anthropic_topk": "Top-k mengubah cara model memilih token untuk output. Top-k 1 berarti token yang dipilih adalah yang paling mungkin di antara semua token dalam kosakata model (juga disebut decoding serakah), sedangkan top-k 3 berarti token berikutnya dipilih dari antara 3 token yang paling mungkin (menggunakan suhu).", "com_endpoint_anthropic_topp": "Top-p mengubah cara model memilih token untuk output. Token dipilih dari yang paling mungkin (lihat parameter topK) hingga yang paling tidak mungkin sampai jumlah probabilitas mereka sama dengan nilai top-p.", "com_endpoint_completion": "Penyelesaian", "com_endpoint_completion_model": "Model Penyelesaian (Direkomendasikan: GPT-4)", "com_endpoint_config_click_here": "Klik <PERSON>", "com_endpoint_config_google_api_info": "Untuk mendapatkan kunci API Bahasa Generat<PERSON> (untuk Gemini),", "com_endpoint_config_google_api_key": "Kunci API Google", "com_endpoint_config_google_cloud_platform": "(dari Google Cloud Platform)", "com_endpoint_config_google_gemini_api": "(Gemini API)", "com_endpoint_config_google_service_key": "<PERSON><PERSON><PERSON>", "com_endpoint_config_key": "Atur Kunci API", "com_endpoint_config_key_encryption": "<PERSON><PERSON><PERSON>a akan dienkripsi dan di<PERSON>pus pada", "com_endpoint_config_key_for": "Atur Kunci API untuk", "com_endpoint_config_key_google_need_to": "<PERSON>a perlu", "com_endpoint_config_key_google_service_account": "<PERSON><PERSON><PERSON>", "com_endpoint_config_key_google_vertex_ai": "Aktifkan Vertex AI", "com_endpoint_config_key_google_vertex_api": "API di Google Cloud, kemudian", "com_endpoint_config_key_google_vertex_api_role": "Pastikan untuk mengklik 'Buat dan Lanjutkan' untuk memberikan setidaknya peran 'Pengguna Vertex AI'. <PERSON><PERSON><PERSON>, buat kunci JSON untuk diimpor di sini.", "com_endpoint_config_key_import_json_key": "Impor Kunci JSON Akun <PERSON>.", "com_endpoint_config_key_import_json_key_invalid": "<PERSON><PERSON>i JSON <PERSON>, <PERSON><PERSON><PERSON><PERSON> file yang benar?", "com_endpoint_config_key_import_json_key_success": "<PERSON><PERSON><PERSON><PERSON>r Kunci JSON Akun <PERSON>", "com_endpoint_config_key_name": "<PERSON><PERSON><PERSON>", "com_endpoint_config_placeholder": "Atur Kunci Anda di menu Header untuk mengobrol.", "com_endpoint_config_value": "<PERSON><PERSON><PERSON><PERSON> nilai untuk", "com_endpoint_context": "Konteks", "com_endpoint_custom_name": "<PERSON><PERSON>", "com_endpoint_default": "default", "com_endpoint_default_blank": "default: kosong", "com_endpoint_default_empty": "default: kosong", "com_endpoint_default_with_num": "default: {{0}}", "com_endpoint_examples": " Preset", "com_endpoint_export": "Ekspor", "com_endpoint_frequency_penalty": "<PERSON><PERSON><PERSON>", "com_endpoint_func_hover": "Aktifkan penggunaan Plugin sebagai Fungsi OpenAI", "com_endpoint_google_custom_name_placeholder": "Tetapkan nama kustom untuk Google", "com_endpoint_google_maxoutputtokens": "<PERSON><PERSON><PERSON> maksimum token yang dapat dihasilkan dalam respons. Tentukan nilai yang lebih rendah untuk respons yang lebih pendek dan nilai yang lebih tinggi untuk respons yang lebih panjang.", "com_endpoint_google_temp": "<PERSON>lai yang lebih tinggi = lebih acak, sedangkan nilai yang lebih rendah = lebih fokus dan deterministik. <PERSON><PERSON>n untuk mengubah ini atau Top P tetapi tidak keduanya.", "com_endpoint_google_topk": "Top-k mengubah cara model memilih token untuk output. Top-k 1 berarti token yang dipilih adalah yang paling mungkin di antara semua token dalam kosakata model (juga disebut decoding serakah), sedangkan top-k 3 berarti token berikutnya dipilih dari antara 3 token yang paling mungkin (menggunakan temperatur).", "com_endpoint_google_topp": "Top-p mengubah cara model memilih token untuk output. Token dipilih dari yang paling mungkin (lihat parameter topK) hingga yang paling tidak mungkin sampai jumlah probabilitas mereka sama dengan nilai top-p.", "com_endpoint_max_output_tokens": "Token Output Maks", "com_endpoint_message": "<PERSON><PERSON>", "com_endpoint_message_not_appendable": "Edit pesan <PERSON><PERSON>au Regenerasi.", "com_endpoint_my_preset": "Preset Saya", "com_endpoint_no_presets": "Belum ada preset, gunakan tombol pengaturan untuk membuat satu", "com_endpoint_open_menu": "<PERSON><PERSON>", "com_endpoint_openai_custom_name_placeholder": "Tetapkan nama kustom untuk ChatGPT", "com_endpoint_openai_detail": "Resolusi untuk permintaan Vision. \"Rendah\" lebih murah dan lebih cepat, \"Tinggi\" lebih detail dan mahal, dan \"O<PERSON><PERSON><PERSON>\" akan secara otomatis memilih antara keduanya berdasarkan resolusi gambar.", "com_endpoint_openai_freq": "Angka antara -2,0 dan 2,0. <PERSON><PERSON> positif menghukum token baru berdasarkan frekuensi mereka yang ada dalam teks sejauh ini, men<PERSON><PERSON><PERSON> kemungkinan model untuk mengulangi baris yang sama secara harfiah.", "com_endpoint_openai_max": "Token maksimum yang akan dihasilkan. Panjang total token masukan dan token yang dihasilkan dibatasi oleh panjang konteks model.", "com_endpoint_openai_pres": "Angka antara -2,0 dan 2,0. <PERSON><PERSON> positif menghukum token baru berdasarkan apakah mereka muncul dalam teks sejauh ini, meningkatkan kemungkinan model untuk berbicara tentang topik baru.", "com_endpoint_openai_prompt_prefix_placeholder": "Tetapkan instruksi kustom untuk dimasukkan dalam Pesan Sistem. Default: tidak ada", "com_endpoint_openai_resend": "<PERSON><PERSON> ulang semua gambar yang sebelumnya dilampirkan. Catatan: ini dapat meningkatkan biaya token secara signifikan dan Anda mungkin mengalami kesalahan dengan banyak lampiran gambar.", "com_endpoint_openai_temp": "<PERSON>lai yang lebih tinggi = lebih acak, sedangkan nilai yang lebih rendah = lebih fokus dan deterministik. <PERSON><PERSON>n untuk mengubah ini atau Top P tetapi tidak keduanya.", "com_endpoint_openai_topp": "Sebuah alternatif untuk pengambilan sampel dengan suhu, disebut pengambilan sampel inti, di mana model me<PERSON>timbang<PERSON> hasil dari token dengan massa probabilitas top_p. Jadi 0,1 berarti hanya token yang mencakup 10% massa probabilitas teratas yang dipertimbangkan. <PERSON><PERSON>n untuk mengubah ini atau suhu tetapi tidak keduanya.", "com_endpoint_output": "Output", "com_endpoint_plug_image_detail": "<PERSON><PERSON><PERSON>", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "Tetapkan instruksi kustom untuk dimasukkan dalam Pesan Sistem. Default: tidak ada", "com_endpoint_plug_skip_completion": "<PERSON><PERSON>", "com_endpoint_plug_use_functions": "<PERSON><PERSON><PERSON>", "com_endpoint_presence_penalty": "<PERSON><PERSON><PERSON>", "com_endpoint_preset": "preset", "com_endpoint_preset_default": "se<PERSON><PERSON> menja<PERSON> preset default.", "com_endpoint_preset_default_item": "Default:", "com_endpoint_preset_default_none": "Tidak ada preset default yang aktif.", "com_endpoint_preset_default_removed": "tidak lagi menjadi preset default.", "com_endpoint_preset_delete_confirm": "Anda yakin ingin menghapus preset ini?", "com_endpoint_preset_delete_error": "<PERSON> kesalahan saat menghapus preset <PERSON><PERSON>. <PERSON>lakan coba lagi.", "com_endpoint_preset_import": "Preset Diimpor!", "com_endpoint_preset_import_error": "<PERSON> kesalahan saat mengimpor preset And<PERSON>. Silakan coba lagi.", "com_endpoint_preset_name": "<PERSON><PERSON>", "com_endpoint_preset_save_error": "<PERSON> kesalahan saat menyi<PERSON>an preset <PERSON><PERSON>. <PERSON>lakan coba lagi.", "com_endpoint_preset_selected": "Preset Aktif!", "com_endpoint_preset_selected_title": "Aktif!", "com_endpoint_preset_title": "Preset", "com_endpoint_presets": "presets", "com_endpoint_presets_clear_warning": "Anda yakin ingin menghapus semua preset? Ini tidak dapat di<PERSON>alkan.", "com_endpoint_prompt_prefix": "<PERSON><PERSON><PERSON> Prompt", "com_endpoint_prompt_prefix_placeholder": "Tetapkan instruksi kustom atau konteks. Diabaikan jika kosong.", "com_endpoint_save_as_preset": "Simpan Sebagai Preset", "com_endpoint_set_custom_name": "<PERSON><PERSON><PERSON><PERSON> nama kustom, jika <PERSON>a dapat menemukan preset ini", "com_endpoint_skip_hover": "Akt<PERSON><PERSON> langkah penye<PERSON><PERSON>n yang di<PERSON>, yang meninjau jawaban akhir dan langkah yang dihasilkan", "com_endpoint_temperature": "Temperatur", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_hide_examples": "Sembunyi<PERSON>", "com_nav_archive_created_at": "TanggalDibuat", "com_nav_archive_name": "<PERSON><PERSON>", "com_nav_archived_chats": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_auto_scroll": "O<PERSON><PERSON><PERSON> gulir ke Baru saat Buka", "com_nav_balance": "Keseimbangan", "com_nav_change_picture": "Ubah foto", "com_nav_clear_all_chats": "<PERSON><PERSON> semua obro<PERSON>", "com_nav_clear_conversation": "<PERSON><PERSON>", "com_nav_clear_conversation_confirm_message": "Anda yakin ingin menghapus semua percakapan? Ini tidak dapat dibatalkan.", "com_nav_close_sidebar": "Tutup sidebar", "com_nav_confirm_clear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_enabled": "Diaktifkan", "com_nav_export": "Ekspor", "com_nav_export_all_message_branches": "Ekspor semua cabang pesan", "com_nav_export_conversation": "Ekspor percakapan", "com_nav_export_filename": "<PERSON><PERSON>", "com_nav_export_filename_placeholder": "Atur nama file", "com_nav_export_include_endpoint_options": "Sertakan opsi endpoint", "com_nav_export_recursive": "Re<PERSON><PERSON><PERSON>", "com_nav_export_recursive_or_sequential": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> be<PERSON>?", "com_nav_export_type": "Tipe", "com_nav_font_size": "<PERSON><PERSON><PERSON> huruf", "com_nav_help_faq": "Bantuan & FAQ", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "<PERSON><PERSON><PERSON> otomatis", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_chinese": "中文", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "Bahasa", "com_nav_latex_parsing": "Parsing LaTeX dalam pesan (dapat memengaruhi kinerja)", "com_nav_log_out": "<PERSON><PERSON><PERSON>", "com_nav_modular_chat": "Aktifkan penggantian Endpoint di tengah percakapan", "com_nav_not_supported": "Tidak <PERSON>", "com_nav_open_sidebar": "Buka sidebar", "com_nav_plugin_auth_error": "<PERSON> kesalahan saat mencoba mengautentikasi plugin ini. <PERSON>lakan coba lagi.", "com_nav_plugin_search": "Cari plugin", "com_nav_plugin_store": "Toko plugin", "com_nav_profile_picture": "Foto Profil", "com_nav_save_drafts": "Simpan draft", "com_nav_search_placeholder": "<PERSON><PERSON> pesan", "com_nav_send_message": "<PERSON><PERSON>", "com_nav_setting_account": "<PERSON><PERSON><PERSON>", "com_nav_setting_beta": "Fitur beta", "com_nav_setting_data": "Kontrol data", "com_nav_setting_general": "<PERSON><PERSON>", "com_nav_settings": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_shared_links": "<PERSON> berb<PERSON>", "com_nav_theme": "<PERSON><PERSON>", "com_nav_theme_dark": "<PERSON><PERSON><PERSON>", "com_nav_theme_light": "Terang", "com_nav_theme_system": "Sistem", "com_nav_user": "PENGGUNA", "com_nav_user_name_display": "<PERSON><PERSON><PERSON><PERSON> nama pengguna dalam pesan", "com_show_agent_settings": "<PERSON><PERSON><PERSON><PERSON>uran Agen", "com_show_completion_settings": "<PERSON><PERSON><PERSON><PERSON>", "com_show_examples": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_accept": "<PERSON><PERSON>", "com_ui_all": "semua", "com_ui_archive": "<PERSON><PERSON><PERSON>", "com_ui_archive_error": "<PERSON><PERSON> mengar<PERSON><PERSON> per<PERSON>", "com_ui_bookmark_delete_confirm": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus penanda ini?", "com_ui_bookmarks": "<PERSON><PERSON>", "com_ui_bookmarks_add_to_conversation": "Tambahkan ke percakapan saat ini", "com_ui_bookmarks_count": "<PERSON><PERSON><PERSON>", "com_ui_bookmarks_create_error": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuat penanda", "com_ui_bookmarks_create_success": "<PERSON><PERSON> be<PERSON> di<PERSON>at", "com_ui_bookmarks_delete_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat menghapus penanda", "com_ui_bookmarks_delete_success": "<PERSON><PERSON> be<PERSON>", "com_ui_bookmarks_description": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_bookmarks_new": "<PERSON><PERSON>", "com_ui_bookmarks_title": "<PERSON><PERSON><PERSON>", "com_ui_bookmarks_update_error": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat memperbarui penanda", "com_ui_bookmarks_update_success": "<PERSON><PERSON> be<PERSON>", "com_ui_cancel": "<PERSON><PERSON>", "com_ui_clear": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_close": "<PERSON><PERSON><PERSON>", "com_ui_confirm_action": "Konfirmasi <PERSON>i", "com_ui_continue": "Lanjutkan", "com_ui_copied_to_clipboard": "<PERSON><PERSON>in ke papan klip", "com_ui_copy_link": "<PERSON><PERSON>an", "com_ui_copy_to_clipboard": "<PERSON>in ke papan klip", "com_ui_create_link": "<PERSON><PERSON><PERSON> tautan", "com_ui_decline": "<PERSON><PERSON> tidak <PERSON>", "com_ui_delete": "Hapus", "com_ui_delete_confirm": "<PERSON>i akan men<PERSON>", "com_ui_delete_conversation": "Hapus chat?", "com_ui_edit": "Edit", "com_ui_enter": "<PERSON><PERSON><PERSON>", "com_ui_examples": "<PERSON><PERSON><PERSON>", "com_ui_happy_birthday": "Ini ulang tahun pertamaku!", "com_ui_import_conversation_error": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mengimpor per<PERSON>pan <PERSON>", "com_ui_import_conversation_info": "Impor percakapan dari file JSON", "com_ui_import_conversation_success": "Percakapan ber<PERSON> diimpor", "com_ui_input": "<PERSON><PERSON><PERSON>", "com_ui_model": "Model", "com_ui_new_chat": "<PERSON><PERSON>", "com_ui_next": "Berikutnya", "com_ui_no_terms_content": "Tidak ada konten syarat dan ketentuan untuk ditampilkan", "com_ui_of": "dari", "com_ui_prev": "Sebelumnya", "com_ui_preview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_regenerate": "Regenerasi", "com_ui_rename": "Renombrar", "com_ui_revoke": "<PERSON><PERSON><PERSON>", "com_ui_revoke_info": "<PERSON><PERSON><PERSON> semua kredensial yang diberikan pengguna", "com_ui_save": "Simpan", "com_ui_save_submit": "Simpan & Kirim", "com_ui_saved": "Tersimpan!", "com_ui_select_model": "<PERSON><PERSON><PERSON> model", "com_ui_share": "Bagikan", "com_ui_share_create_message": "<PERSON>a Anda dan pesan apa pun yang Anda tambahkan setelah berbagi tetap pribadi.", "com_ui_share_delete_error": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat menghapus tautan yang dibagikan.", "com_ui_share_error": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membagikan tautan chat", "com_ui_share_link_to_chat": "Bagikan tautan ke chat", "com_ui_share_update_message": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan pesan apa pun yang <PERSON>a tambahkan setelah berbagi tetap pribadi.", "com_ui_shared_link_not_found": "<PERSON>tan berbagi tidak di<PERSON>ukan", "com_ui_stop": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_submit": "<PERSON><PERSON>", "com_ui_terms_and_conditions": "<PERSON><PERSON><PERSON> dan <PERSON>", "com_ui_unarchive": "<PERSON><PERSON>", "com_ui_unarchive_error": "<PERSON><PERSON> membuka arsip", "com_ui_upload": "<PERSON><PERSON><PERSON>", "com_ui_upload_error": "<PERSON> kesalahan saat mengunggah file Anda", "com_ui_upload_success": "<PERSON><PERSON><PERSON><PERSON> file", "com_ui_use_prompt": "<PERSON><PERSON><PERSON>", "com_user_message": "<PERSON><PERSON>"}